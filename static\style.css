* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.dashboard {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.status-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border-left: 4px solid #007bff;
    transition: transform 0.2s ease;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.status-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.status-header i {
    font-size: 1.5rem;
    margin-right: 10px;
    color: #007bff;
}

.status-header h3 {
    font-size: 1.2rem;
    color: #333;
}

.status-content {
    color: #666;
}

.status-text {
    font-weight: 600;
    margin-bottom: 8px;
    color: #333;
}

.last-check, .last-post, .stat {
    font-size: 0.9rem;
    color: #888;
}

.controls {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
    margin-bottom: 30px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
}

.btn-small {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.errors-section {
    background: #fff5f5;
    border: 1px solid #fed7d7;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
}

.errors-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 15px;
}

.errors-header h3 {
    color: #e53e3e;
    display: flex;
    align-items: center;
    gap: 8px;
}

.errors-list {
    max-height: 200px;
    overflow-y: auto;
}

.error-item {
    background: white;
    padding: 10px;
    margin-bottom: 8px;
    border-radius: 5px;
    border-left: 3px solid #e53e3e;
    font-size: 0.9rem;
}

.loading {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 20px 40px;
    border-radius: 10px;
    font-size: 1.2rem;
    z-index: 1000;
}

.loading i {
    margin-right: 10px;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .dashboard {
        padding: 20px;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
    
    .controls {
        flex-direction: column;
        align-items: center;
    }
    
    .btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}

/* Animation for status updates */
.status-text {
    transition: color 0.3s ease;
}

.status-card.updated .status-text {
    color: #28a745;
}

/* Success/Error states */
.status-success {
    color: #28a745 !important;
}

.status-error {
    color: #dc3545 !important;
}

.status-warning {
    color: #ffc107 !important;
}
