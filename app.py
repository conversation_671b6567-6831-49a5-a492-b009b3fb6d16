from flask import Flask, render_template, jsonify, request, flash, redirect, url_for
from apscheduler.schedulers.background import BackgroundScheduler
from datetime import datetime
import logging
import json
import os

from config import Config
from email_monitor import EmailMonitor
from content_generator import ContentGenerator
from linkedin_poster import LinkedInPoster

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = Config.SECRET_KEY

# Global variables to store state
last_check_time = None
last_post_time = None
processed_emails = set()
app_status = {
    'email_monitor': 'Not started',
    'linkedin_poster': 'Not started',
    'content_generator': 'Not started',
    'last_check': None,
    'last_post': None,
    'total_posts': 0,
    'errors': []
}

# Initialize components
email_monitor = EmailMonitor()
content_generator = ContentGenerator()
linkedin_poster = LinkedInPoster()

def check_and_post():
    """Main function to check emails and create LinkedIn posts"""
    global last_check_time, last_post_time, processed_emails, app_status

    try:
        logger.info("Starting email check and post process...")
        app_status['last_check'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # Check if configuration is valid (suppress print output)
        try:
            config_valid = Config.validate_config()
        except:
            config_valid = False

        if not config_valid:
            app_status['email_monitor'] = 'Configuration incomplete'
            app_status['errors'].append('Please update .env file with actual credentials')
            return

        # Check for new emails
        new_emails = email_monitor.check_for_new_emails()

        if not new_emails:
            logger.info("No new emails found")
            app_status['email_monitor'] = 'No new emails'
            return

        app_status['email_monitor'] = f'Found {len(new_emails)} new emails'

        for email_data in new_emails:
            email_id = email_data['id']

            # Skip if already processed
            if email_id in processed_emails:
                continue

            logger.info(f"Processing email: {email_data['subject']}")

            # Generate LinkedIn post content
            post_content = content_generator.generate_linkedin_post(
                email_data['subject'],
                email_data['content']
            )

            if not post_content:
                logger.error("Failed to generate post content")
                app_status['content_generator'] = 'Failed to generate content'
                app_status['errors'].append(f"Content generation failed for: {email_data['subject']}")
                continue

            app_status['content_generator'] = 'Content generated successfully'

            # Post to LinkedIn
            if linkedin_poster.create_and_post(post_content):
                logger.info("Successfully posted to LinkedIn")
                app_status['linkedin_poster'] = 'Posted successfully'
                app_status['last_post'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                app_status['total_posts'] += 1
                processed_emails.add(email_id)
                last_post_time = datetime.now()
            else:
                logger.error("Failed to post to LinkedIn")
                app_status['linkedin_poster'] = 'Failed to post'
                app_status['errors'].append(f"LinkedIn posting failed for: {email_data['subject']}")

        last_check_time = datetime.now()

    except Exception as e:
        logger.error(f"Error in check_and_post: {e}")
        app_status['errors'].append(f"System error: {str(e)}")

@app.route('/')
def index():
    """Main dashboard"""
    return render_template('index.html', status=app_status)

@app.route('/api/status')
def get_status():
    """API endpoint to get current status"""
    return jsonify(app_status)

@app.route('/api/check-now', methods=['POST'])
def check_now():
    """Manually trigger email check and posting"""
    try:
        check_and_post()
        return jsonify({'success': True, 'message': 'Check completed'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/test-email')
def test_email():
    """Test email connection"""
    try:
        mail = email_monitor.connect_to_email()
        if mail:
            mail.close()
            mail.logout()
            return jsonify({'success': True, 'message': 'Email connection successful'})
        else:
            return jsonify({'success': False, 'error': 'Failed to connect to email'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/test-linkedin')
def test_linkedin():
    """Test LinkedIn authentication"""
    try:
        if linkedin_poster.authenticate():
            return jsonify({'success': True, 'message': 'LinkedIn authentication successful'})
        else:
            return jsonify({'success': False, 'error': 'LinkedIn authentication failed'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/clear-errors', methods=['POST'])
def clear_errors():
    """Clear error log"""
    global app_status
    app_status['errors'] = []
    return jsonify({'success': True})

def start_scheduler():
    """Start the background scheduler"""
    scheduler = BackgroundScheduler()
    scheduler.add_job(
        func=check_and_post,
        trigger="interval",
        minutes=Config.CHECK_INTERVAL_MINUTES,
        id='email_check_job'
    )
    scheduler.start()
    logger.info(f"Scheduler started - checking every {Config.CHECK_INTERVAL_MINUTES} minutes")

def initialize_app():
    """Initialize the application"""
    try:
        # Validate configuration
        config_valid = Config.validate_config()
        if config_valid:
            logger.info("Configuration validated successfully")
            # Start the scheduler only if config is valid
            start_scheduler()
        else:
            logger.warning("Configuration has placeholder values - some features may not work")
            logger.info("Scheduler not started due to incomplete configuration")
    except Exception as e:
        logger.error(f"Initialization error: {e}")

if __name__ == '__main__':
    try:
        initialize_app()
        # Run the Flask app
        app.run(debug=False, host='0.0.0.0', port=5000)
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        print(f"Error starting application: {e}")
