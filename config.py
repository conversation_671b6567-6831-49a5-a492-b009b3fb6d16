import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    # Email settings
    EMAIL_HOST = os.getenv('EMAIL_HOST', 'imap.gmail.com')
    EMAIL_PORT = int(os.getenv('EMAIL_PORT', 993))
    EMAIL_USERNAME = os.getenv('EMAIL_USERNAME')
    EMAIL_PASSWORD = os.getenv('EMAIL_PASSWORD')
    EMAIL_USE_SSL = os.getenv('EMAIL_USE_SSL', 'True').lower() == 'true'

    # LinkedIn settings
    LINKEDIN_USERNAME = os.getenv('LINKEDIN_USERNAME')
    LINKEDIN_PASSWORD = os.getenv('LINKEDIN_PASSWORD')
    LINKEDIN_ACCESS_TOKEN = os.getenv('LINKEDIN_ACCESS_TOKEN')

    # OpenAI settings
    OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')

    # Flask settings
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'dev-secret-key')
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    # App settings
    CHECK_INTERVAL_MINUTES = int(os.getenv('CHECK_INTERVAL_MINUTES', 30))
    TARGET_EMAIL_SENDER = os.getenv('TARGET_EMAIL_SENDER', '<EMAIL>')

    @classmethod
    def validate_config(cls):
        """Validate that all required configuration is present"""
        required_vars = [
            'EMAIL_USERNAME', 'EMAIL_PASSWORD',
            'LINKEDIN_USERNAME', 'LINKEDIN_PASSWORD',
            'OPENAI_API_KEY'
        ]

        missing_vars = []
        placeholder_vars = []

        for var in required_vars:
            value = getattr(cls, var)
            if not value:
                missing_vars.append(var)
            elif value.startswith('your_'):
                placeholder_vars.append(var)

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

        if placeholder_vars:
            print(f"⚠️  Warning: The following variables contain placeholder values: {', '.join(placeholder_vars)}")
            print("   Please update your .env file with actual credentials for full functionality.")
            return False

        return True
