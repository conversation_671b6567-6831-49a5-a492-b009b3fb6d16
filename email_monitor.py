import imaplib
import email
from email.header import decode_header
from datetime import datetime, timedelta
import logging
from config import Config
from bs4 import BeautifulSoup
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailMonitor:
    def __init__(self):
        self.config = Config()
        self.last_check_time = None
        
    def connect_to_email(self):
        """Connect to email server"""
        try:
            if self.config.EMAIL_USE_SSL:
                mail = imaplib.IMAP4_SSL(self.config.EMAIL_HOST, self.config.EMAIL_PORT)
            else:
                mail = imaplib.IMAP4(self.config.EMAIL_HOST, self.config.EMAIL_PORT)
            
            mail.login(self.config.EMAIL_USERNAME, self.config.EMAIL_PASSWORD)
            return mail
        except Exception as e:
            logger.error(f"Failed to connect to email: {e}")
            return None
    
    def decode_mime_words(self, s):
        """Decode MIME encoded words"""
        decoded_words = decode_header(s)
        decoded_string = ''
        for word, encoding in decoded_words:
            if isinstance(word, bytes):
                word = word.decode(encoding or 'utf-8')
            decoded_string += word
        return decoded_string
    
    def extract_email_content(self, msg):
        """Extract text content from email message"""
        content = ""
        
        if msg.is_multipart():
            for part in msg.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                if content_type == "text/plain" and "attachment" not in content_disposition:
                    body = part.get_payload(decode=True).decode()
                    content += body
                elif content_type == "text/html" and "attachment" not in content_disposition:
                    body = part.get_payload(decode=True).decode()
                    # Parse HTML and extract text
                    soup = BeautifulSoup(body, 'html.parser')
                    content += soup.get_text()
        else:
            content_type = msg.get_content_type()
            if content_type == "text/plain":
                content = msg.get_payload(decode=True).decode()
            elif content_type == "text/html":
                body = msg.get_payload(decode=True).decode()
                soup = BeautifulSoup(body, 'html.parser')
                content = soup.get_text()
        
        return content.strip()
    
    def check_for_new_emails(self):
        """Check for new emails from target sender"""
        mail = self.connect_to_email()
        if not mail:
            return []
        
        try:
            mail.select('inbox')
            
            # Search for emails from target sender
            search_criteria = f'FROM "{self.config.TARGET_EMAIL_SENDER}"'
            
            # If we have a last check time, only get emails since then
            if self.last_check_time:
                since_date = self.last_check_time.strftime("%d-%b-%Y")
                search_criteria += f' SINCE "{since_date}"'
            
            status, messages = mail.search(None, search_criteria)
            
            if status != 'OK':
                logger.error("Failed to search emails")
                return []
            
            email_ids = messages[0].split()
            new_emails = []
            
            for email_id in email_ids:
                status, msg_data = mail.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    continue
                
                msg = email.message_from_bytes(msg_data[0][1])
                
                # Get email date
                date_str = msg.get('Date')
                email_date = email.utils.parsedate_to_datetime(date_str)
                
                # Skip if email is older than last check
                if self.last_check_time and email_date <= self.last_check_time:
                    continue
                
                # Extract email details
                subject = self.decode_mime_words(msg.get('Subject', ''))
                sender = self.decode_mime_words(msg.get('From', ''))
                content = self.extract_email_content(msg)
                
                new_emails.append({
                    'id': email_id.decode(),
                    'subject': subject,
                    'sender': sender,
                    'content': content,
                    'date': email_date
                })
                
                logger.info(f"Found new email: {subject}")
            
            # Update last check time
            self.last_check_time = datetime.now()
            
            return new_emails
            
        except Exception as e:
            logger.error(f"Error checking emails: {e}")
            return []
        finally:
            try:
                mail.close()
                mail.logout()
            except:
                pass
    
    def get_latest_email(self):
        """Get the most recent email from target sender"""
        emails = self.check_for_new_emails()
        if emails:
            return max(emails, key=lambda x: x['date'])
        return None
