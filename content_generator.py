import openai
from config import Config
import logging
import re

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ContentGenerator:
    def __init__(self):
        self.config = Config()
        openai.api_key = self.config.OPENAI_API_KEY
        
    def clean_email_content(self, content):
        """Clean and prepare email content for processing"""
        # Remove excessive whitespace
        content = re.sub(r'\s+', ' ', content)
        
        # Remove common email footers and headers
        content = re.sub(r'unsubscribe.*$', '', content, flags=re.IGNORECASE)
        content = re.sub(r'view.*browser.*$', '', content, flags=re.IGNORECASE)
        
        # Limit content length for API efficiency
        if len(content) > 3000:
            content = content[:3000] + "..."
        
        return content.strip()
    
    def generate_linkedin_post(self, email_subject, email_content):
        """Generate LinkedIn post content from email"""
        try:
            cleaned_content = self.clean_email_content(email_content)
            
            prompt = f"""
            Create a professional LinkedIn post based on this newsletter content from The Rundown AI.
            
            Subject: {email_subject}
            Content: {cleaned_content}
            
            Guidelines:
            - Make it engaging and professional
            - Include 2-3 relevant hashtags
            - Keep it under 300 words
            - Focus on the most interesting insights or news
            - Add a call-to-action or question to encourage engagement
            - Use a conversational tone suitable for LinkedIn
            - Don't mention that this is from an email newsletter
            
            LinkedIn Post:
            """
            
            response = openai.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a social media expert who creates engaging LinkedIn posts about AI and technology news."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.7
            )
            
            generated_post = response.choices[0].message.content.strip()
            
            # Clean up the response
            if generated_post.startswith("LinkedIn Post:"):
                generated_post = generated_post.replace("LinkedIn Post:", "").strip()
            
            logger.info("Successfully generated LinkedIn post")
            return generated_post
            
        except Exception as e:
            logger.error(f"Error generating LinkedIn post: {e}")
            return None
    
    def generate_summary_post(self, email_subject, email_content):
        """Generate a shorter summary post"""
        try:
            cleaned_content = self.clean_email_content(email_content)
            
            prompt = f"""
            Create a concise LinkedIn post summarizing the key points from this AI newsletter.
            
            Subject: {email_subject}
            Content: {cleaned_content}
            
            Requirements:
            - Maximum 150 words
            - Highlight 2-3 key insights
            - Include relevant hashtags
            - Professional but engaging tone
            
            Summary Post:
            """
            
            response = openai.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a professional content creator specializing in AI and tech summaries."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=300,
                temperature=0.6
            )
            
            summary_post = response.choices[0].message.content.strip()
            
            if summary_post.startswith("Summary Post:"):
                summary_post = summary_post.replace("Summary Post:", "").strip()
            
            return summary_post
            
        except Exception as e:
            logger.error(f"Error generating summary post: {e}")
            return None
