<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Social Poster - Email to LinkedIn Automation</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-robot"></i> Social Poster</h1>
            <p>Automated Email to LinkedIn Posting</p>
        </header>

        <div class="dashboard">
            <div class="status-grid">
                <div class="status-card">
                    <div class="status-header">
                        <i class="fas fa-envelope"></i>
                        <h3>Email Monitor</h3>
                    </div>
                    <div class="status-content">
                        <p class="status-text" id="email-status">{{ status.email_monitor }}</p>
                        <p class="last-check">Last Check: <span id="last-check">{{ status.last_check or 'Never' }}</span></p>
                    </div>
                </div>

                <div class="status-card">
                    <div class="status-header">
                        <i class="fas fa-brain"></i>
                        <h3>Content Generator</h3>
                    </div>
                    <div class="status-content">
                        <p class="status-text" id="content-status">{{ status.content_generator }}</p>
                    </div>
                </div>

                <div class="status-card">
                    <div class="status-header">
                        <i class="fab fa-linkedin"></i>
                        <h3>LinkedIn Poster</h3>
                    </div>
                    <div class="status-content">
                        <p class="status-text" id="linkedin-status">{{ status.linkedin_poster }}</p>
                        <p class="last-post">Last Post: <span id="last-post">{{ status.last_post or 'Never' }}</span></p>
                    </div>
                </div>

                <div class="status-card">
                    <div class="status-header">
                        <i class="fas fa-chart-line"></i>
                        <h3>Statistics</h3>
                    </div>
                    <div class="status-content">
                        <p class="stat">Total Posts: <span id="total-posts">{{ status.total_posts }}</span></p>
                        <p class="stat">Monitoring: <EMAIL></p>
                    </div>
                </div>
            </div>

            <div class="controls">
                <button id="check-now-btn" class="btn btn-primary">
                    <i class="fas fa-sync-alt"></i> Check Now
                </button>
                <button id="test-email-btn" class="btn btn-secondary">
                    <i class="fas fa-envelope-open"></i> Test Email
                </button>
                <button id="test-linkedin-btn" class="btn btn-secondary">
                    <i class="fab fa-linkedin"></i> Test LinkedIn
                </button>
                <button id="refresh-btn" class="btn btn-info">
                    <i class="fas fa-refresh"></i> Refresh Status
                </button>
            </div>

            <div class="errors-section" id="errors-section" style="display: none;">
                <div class="errors-header">
                    <h3><i class="fas fa-exclamation-triangle"></i> Errors</h3>
                    <button id="clear-errors-btn" class="btn btn-small">Clear</button>
                </div>
                <div class="errors-list" id="errors-list">
                    <!-- Errors will be populated here -->
                </div>
            </div>
        </div>

        <div class="loading" id="loading" style="display: none;">
            <i class="fas fa-spinner fa-spin"></i> Processing...
        </div>
    </div>

    <script>
        // Auto-refresh status every 30 seconds
        setInterval(refreshStatus, 30000);

        // Button event listeners
        document.getElementById('check-now-btn').addEventListener('click', checkNow);
        document.getElementById('test-email-btn').addEventListener('click', testEmail);
        document.getElementById('test-linkedin-btn').addEventListener('click', testLinkedIn);
        document.getElementById('refresh-btn').addEventListener('click', refreshStatus);
        document.getElementById('clear-errors-btn').addEventListener('click', clearErrors);

        function showLoading() {
            document.getElementById('loading').style.display = 'block';
        }

        function hideLoading() {
            document.getElementById('loading').style.display = 'none';
        }

        function showMessage(message, type = 'info') {
            // Simple alert for now - could be enhanced with toast notifications
            alert(message);
        }

        async function checkNow() {
            showLoading();
            try {
                const response = await fetch('/api/check-now', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    showMessage('Email check completed!', 'success');
                    refreshStatus();
                } else {
                    showMessage('Error: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('Network error: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        async function testEmail() {
            showLoading();
            try {
                const response = await fetch('/api/test-email');
                const data = await response.json();
                
                if (data.success) {
                    showMessage('Email connection successful!', 'success');
                } else {
                    showMessage('Email test failed: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('Network error: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        async function testLinkedIn() {
            showLoading();
            try {
                const response = await fetch('/api/test-linkedin');
                const data = await response.json();
                
                if (data.success) {
                    showMessage('LinkedIn authentication successful!', 'success');
                } else {
                    showMessage('LinkedIn test failed: ' + data.error, 'error');
                }
            } catch (error) {
                showMessage('Network error: ' + error.message, 'error');
            } finally {
                hideLoading();
            }
        }

        async function refreshStatus() {
            try {
                const response = await fetch('/api/status');
                const status = await response.json();
                
                // Update status displays
                document.getElementById('email-status').textContent = status.email_monitor;
                document.getElementById('content-status').textContent = status.content_generator;
                document.getElementById('linkedin-status').textContent = status.linkedin_poster;
                document.getElementById('last-check').textContent = status.last_check || 'Never';
                document.getElementById('last-post').textContent = status.last_post || 'Never';
                document.getElementById('total-posts').textContent = status.total_posts;
                
                // Update errors
                const errorsSection = document.getElementById('errors-section');
                const errorsList = document.getElementById('errors-list');
                
                if (status.errors && status.errors.length > 0) {
                    errorsSection.style.display = 'block';
                    errorsList.innerHTML = status.errors.map(error => 
                        `<div class="error-item">${error}</div>`
                    ).join('');
                } else {
                    errorsSection.style.display = 'none';
                }
                
            } catch (error) {
                console.error('Failed to refresh status:', error);
            }
        }

        async function clearErrors() {
            try {
                const response = await fetch('/api/clear-errors', { method: 'POST' });
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('errors-section').style.display = 'none';
                }
            } catch (error) {
                console.error('Failed to clear errors:', error);
            }
        }

        // Initial status refresh
        refreshStatus();
    </script>
</body>
</html>
