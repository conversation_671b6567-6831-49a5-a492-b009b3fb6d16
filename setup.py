#!/usr/bin/env python3
"""
Setup script for Social Poster application
"""

import os
import sys
import subprocess

def install_requirements():
    """Install Python requirements"""
    print("Installing Python requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def check_env_file():
    """Check if .env file exists and has required variables"""
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        print("Please copy .env.example to .env and fill in your credentials")
        return False
    
    required_vars = [
        'EMAIL_USERNAME',
        'EMAIL_PASSWORD', 
        'LINKEDIN_USERNAME',
        'LINKEDIN_PASSWORD',
        'OPENAI_API_KEY'
    ]
    
    missing_vars = []
    
    try:
        with open('.env', 'r') as f:
            content = f.read()
            
        for var in required_vars:
            if f"{var}=your_" in content or f"{var}=" not in content:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"❌ Missing or incomplete environment variables: {', '.join(missing_vars)}")
            print("Please update your .env file with actual values")
            return False
        else:
            print("✅ Environment variables configured")
            return True
            
    except Exception as e:
        print(f"❌ Error reading .env file: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    dirs = ['templates', 'static', 'logs']
    for dir_name in dirs:
        if not os.path.exists(dir_name):
            os.makedirs(dir_name)
            print(f"✅ Created directory: {dir_name}")

def main():
    """Main setup function"""
    print("🚀 Setting up Social Poster application...")
    print("=" * 50)
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        sys.exit(1)
    
    # Check environment configuration
    if not check_env_file():
        print("\n📝 Next steps:")
        print("1. Update the .env file with your actual credentials")
        print("2. Run 'python app.py' to start the application")
        sys.exit(1)
    
    print("\n🎉 Setup completed successfully!")
    print("\n📝 Next steps:")
    print("1. Run 'python app.py' to start the application")
    print("2. Open http://localhost:5000 in your browser")
    print("3. Test your email and LinkedIn connections")
    print("4. The app will automatically check for emails every 30 minutes")

if __name__ == "__main__":
    main()
