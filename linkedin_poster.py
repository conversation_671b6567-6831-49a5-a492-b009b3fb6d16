import requests
import json
import logging
from config import Config
from linkedin_api import Linkedin
import time

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LinkedInPoster:
    def __init__(self):
        self.config = Config()
        self.api = None
        self.person_id = None
        
    def authenticate(self):
        """Authenticate with LinkedIn"""
        try:
            # Using linkedin-api library for easier authentication
            self.api = Linkedin(
                self.config.LINKEDIN_USERNAME, 
                self.config.LINKEDIN_PASSWORD
            )
            
            # Get user profile to verify authentication
            profile = self.api.get_profile()
            self.person_id = profile.get('public_identifier')
            
            logger.info(f"Successfully authenticated with LinkedIn as {profile.get('firstName', '')} {profile.get('lastName', '')}")
            return True
            
        except Exception as e:
            logger.error(f"LinkedIn authentication failed: {e}")
            return False
    
    def post_to_linkedin(self, content):
        """Post content to LinkedIn"""
        if not self.api:
            if not self.authenticate():
                return False
        
        try:
            # Create the post
            response = self.api.post_update(content)
            
            if response:
                logger.info("Successfully posted to LinkedIn")
                return True
            else:
                logger.error("Failed to post to LinkedIn")
                return False
                
        except Exception as e:
            logger.error(f"Error posting to LinkedIn: {e}")
            return False
    
    def post_with_retry(self, content, max_retries=3):
        """Post with retry logic"""
        for attempt in range(max_retries):
            try:
                if self.post_to_linkedin(content):
                    return True
                
                if attempt < max_retries - 1:
                    logger.info(f"Retrying LinkedIn post (attempt {attempt + 2}/{max_retries})")
                    time.sleep(5)  # Wait 5 seconds before retry
                    
            except Exception as e:
                logger.error(f"Attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(5)
        
        logger.error("All retry attempts failed")
        return False
    
    def validate_post_content(self, content):
        """Validate post content before posting"""
        if not content or not content.strip():
            logger.error("Post content is empty")
            return False
        
        # LinkedIn post character limit
        if len(content) > 3000:
            logger.warning("Post content exceeds LinkedIn character limit")
            return False
        
        return True
    
    def create_and_post(self, content):
        """Validate and post content to LinkedIn"""
        if not self.validate_post_content(content):
            return False
        
        logger.info("Attempting to post to LinkedIn...")
        logger.info(f"Post preview: {content[:100]}...")
        
        return self.post_with_retry(content)
